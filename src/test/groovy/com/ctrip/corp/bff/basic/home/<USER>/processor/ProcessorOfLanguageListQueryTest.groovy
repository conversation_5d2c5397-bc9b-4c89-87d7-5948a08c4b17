package com.ctrip.corp.bff.basic.home.trip.processor

import com.ctrip.corp.bff.basic.home.trip.contract.LanguageListQueryRequestVO
import com.ctrip.corp.bff.basic.home.trip.contract.LanguageListQueryResponseVO
import com.ctrip.corp.bff.basic.home.trip.handler.handlerofcorp4jserviceclient.HandlerOfGetCorpInfo
import com.ctrip.corp.bff.basic.home.trip.handler.handlerofcorpaccountqueryservice.HandlerOfGeneralSearchAccountInfo
import com.ctrip.corp.bff.basic.home.trip.handler.handlerofcorpuserinfoservice4jclient.HandlerOfGetCorpUserInfo
import com.ctrip.corp.bff.basic.home.trip.mapper.mapperofcorpaccountqueryserviceclient.MapperofSoaGeneralSearchAccountInfoRequest
import com.ctrip.corp.bff.basic.home.trip.mapper.mapperoflanguagelistget.MapperOfLanguageList
import com.ctrip.corp.bff.basic.home.trip.mapper.mapperoflanguagelistget.MapperOfSoaGetCorpInfoRequest
import com.ctrip.corp.bff.basic.home.trip.mapper.mapperofnavheaderinit.MapperOfGetCorpUserInfoRequest
import com.ctrip.corp.bff.framework.template.entity.TemplateHeader
import com.ctrip.corp.bff.framework.template.entity.TemplateSoaRequestType
import com.ctrip.corp.bff.framework.template.handler.WaitFuture
import spock.lang.Specification


class ProcessorOfLanguageListQueryTest extends Specification {
    HandlerOfGetCorpInfo handlerOfGetCorpInfo = Mock()
    MapperOfSoaGetCorpInfoRequest mapperOfSoaGetCorpInfoRequest = Mock()
    MapperOfLanguageList mapperOfLanguageList = Mock()
    HandlerOfGeneralSearchAccountInfo handlerOfGeneralSearchAccountInfo = Mock()
    MapperofSoaGeneralSearchAccountInfoRequest mapperofSoaGeneralSearchAccountInfoRequest = Mock()
    HandlerOfGetCorpUserInfo handlerOfGetCorpUserInfo = Mock()
    MapperOfGetCorpUserInfoRequest mapperOfGetCorpUserInfoRequest = Mock()
    ProcessorOfLanguageListQuery processorOfLanguageListQuery = new ProcessorOfLanguageListQuery
            (
                    handlerOfGetCorpInfo: handlerOfGetCorpInfo,
                    mapperOfSoaGetCorpInfoRequest: mapperOfSoaGetCorpInfoRequest,
                    mapperOfLanguageList: mapperOfLanguageList,
                    handlerOfGeneralSearchAccountInfo: handlerOfGeneralSearchAccountInfo,
                    mapperofSoaGeneralSearchAccountInfoRequest: mapperofSoaGeneralSearchAccountInfoRequest,
                    handlerOfGetCorpUserInfo: handlerOfGetCorpUserInfo,
                    mapperOfGetCorpUserInfoRequest: mapperOfGetCorpUserInfoRequest
            )


    def setup() {

    }


    def "test execute"() {
        given:
        handlerOfGetCorpInfo.handleAsync(_) >> Mock(WaitFuture)
        handlerOfGeneralSearchAccountInfo.handleAsync(_) >> Mock(WaitFuture)
        handlerOfGetCorpUserInfo.handleAsync(_) >> Mock(WaitFuture)
        mapperOfGetCorpUserInfoRequest.map(_) >> Mock()
        mapperOfSoaGetCorpInfoRequest.map(_) >> Mock()
        mapperofSoaGeneralSearchAccountInfoRequest.map(_) >> Mock()
        mapperOfLanguageList.map(_) >> []
        when:
        LanguageListQueryResponseVO result = processorOfLanguageListQuery.execute(new LanguageListQueryRequestVO(
                requestHeader: new TemplateSoaRequestType(
                        header: new TemplateHeader(
                                corpId: "1", userId: "uid"))))
        then:
        result != null
    }

}