package com.ctrip.corp.bff.basic.home.trip.mapper.mapperoflanguagelistget


import com.ctrip.corp.bff.basic.home.trip.contract.LanguageListQueryRequestVO
import com.ctrip.corp.bff.basic.home.trip.qconfig.languagelist.LanguageConfig
import com.ctrip.corp.bff.framework.template.common.serialize.JsonUtil
import com.ctrip.corp.bff.framework.template.entity.TemplateHeader
import com.ctrip.corp.bff.framework.template.entity.TemplateSoaRequestType
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple3
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple4
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple5
import corp.user.service.CorpAccountQueryService.GeneralSearchAccountInfoResponseType
import corp.user.service.corp4jservice.GetCorpInfoResponseType
import corp.user.service.corpUserInfoService.GetCorpUserInfoResponseType
import spock.lang.Specification
import spock.lang.Unroll

class MapperOfLanguageListTest extends Specification {
    def mapperOfLanguageList = new MapperOfLanguageList()

    void setup() {
    }

    @Unroll
    def legalDocumentQueryTest() {
        given:
        LanguageConfig languageConfig1 = JsonUtil.fromJson("{\n" +
                "    \"hostConfigMap\": {\n" +
                "        \"biz.trip.com\": [\n" +
                "            {\n" +
                "                \"language\": \"en-US\",\n" +
                "                \"sharkKey\": \"key.corp.public.ibc.layout.us\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"language\": \"ja-JP\",\n" +
                "                \"sharkKey\": \"key.corp.public.ibc.layout.jp\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"language\": \"zh-HK\",\n" +
                "                \"sharkKey\": \"key.corp.public.ibc.layout.hk\"\n" +
                "            }\n" +
                "        ],\n" +
                "        \"www.trip.biz\": [\n" +
                "            {\n" +
                "                \"language\": \"en-US\",\n" +
                "                \"sharkKey\": \"key.corp.public.ibc.layout.us\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"language\": \"ja-JP\",\n" +
                "                \"sharkKey\": \"key.corp.public.ibc.layout.jp\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"language\": \"zh-HK\",\n" +
                "                \"sharkKey\": \"key.corp.public.ibc.layout.hk\"\n" +
                "            }\n" +
                "        ],\n" +
                "        \"hk.trip.biz\": [\n" +
                "            {\n" +
                "                \"language\": \"en-US\",\n" +
                "                \"sharkKey\": \"key.corp.public.ibc.layout.us\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"language\": \"ja-JP\",\n" +
                "                \"sharkKey\": \"key.corp.public.ibc.layout.jp\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"language\": \"zh-HK\",\n" +
                "                \"sharkKey\": \"key.corp.public.ibc.layout.hk\"\n" +
                "            }\n" +
                "        ],\n" +
                "        \"jp.trip.biz\": [\n" +
                "            {\n" +
                "                \"language\": \"en-US\",\n" +
                "                \"sharkKey\": \"key.corp.public.ibc.layout.us\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"language\": \"ja-JP\",\n" +
                "                \"sharkKey\": \"key.corp.public.ibc.layout.jp\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"language\": \"zh-HK\",\n" +
                "                \"sharkKey\": \"key.corp.public.ibc.layout.hk\"\n" +
                "            }\n" +
                "        ],\n" +
                "        \"sg.trip.biz\": [\n" +
                "            {\n" +
                "                \"language\": \"en-US\",\n" +
                "                \"sharkKey\": \"key.corp.public.ibc.layout.us\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"language\": \"ja-JP\",\n" +
                "                \"sharkKey\": \"key.corp.public.ibc.layout.jp\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"language\": \"zh-HK\",\n" +
                "                \"sharkKey\": \"key.corp.public.ibc.layout.hk\"\n" +
                "            }\n" +
                "        ],\n" +
                "        \"biz.trip.fat4.tripqate.com\": [\n" +
                "            {\n" +
                "                \"language\": \"en-US\",\n" +
                "                \"sharkKey\": \"key.corp.public.ibc.layout.us\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"language\": \"ja-JP\",\n" +
                "                \"sharkKey\": \"key.corp.public.ibc.layout.jp\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"language\": \"zh-HK\",\n" +
                "                \"sharkKey\": \"key.corp.public.ibc.layout.hk\"\n" +
                "            }\n" +
                "        ],\n" +
                "        \"biz.trip.fat24.tripqate.com\": [\n" +
                "            {\n" +
                "                \"language\": \"en-US\",\n" +
                "                \"sharkKey\": \"key.corp.public.ibc.layout.us\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"language\": \"ja-JP\",\n" +
                "                \"sharkKey\": \"key.corp.public.ibc.layout.jp\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"language\": \"zh-HK\",\n" +
                "                \"sharkKey\": \"key.corp.public.ibc.layout.hk\"\n" +
                "            }\n" +
                "        ],\n" +
                "        \"biz.trip.fat25.tripqate.com\": [\n" +
                "            {\n" +
                "                \"language\": \"en-US\",\n" +
                "                \"sharkKey\": \"key.corp.public.ibc.layout.us\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"language\": \"ja-JP\",\n" +
                "                \"sharkKey\": \"key.corp.public.ibc.layout.jp\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"language\": \"zh-HK\",\n" +
                "                \"sharkKey\": \"key.corp.public.ibc.layout.hk\"\n" +
                "            }\n" +
                "        ],\n" +
                "        \"biz.trip.fat92.tripqate.com\": [\n" +
                "            {\n" +
                "                \"language\": \"en-US\",\n" +
                "                \"sharkKey\": \"key.corp.public.ibc.layout.us\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"language\": \"ja-JP\",\n" +
                "                \"sharkKey\": \"key.corp.public.ibc.layout.jp\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"language\": \"zh-HK\",\n" +
                "                \"sharkKey\": \"key.corp.public.ibc.layout.hk\"\n" +
                "            }\n" +
                "        ],\n" +
                "        \"biz.trip.fat11.tripqate.com\": [\n" +
                "            {\n" +
                "                \"language\": \"en-US\",\n" +
                "                \"sharkKey\": \"key.corp.public.ibc.layout.us\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"language\": \"zh-HK\",\n" +
                "                \"sharkKey\": \"key.corp.public.ibc.layout.hk\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"language\": \"ja-JP\",\n" +
                "                \"sharkKey\": \"key.corp.public.ibc.layout.jp\"\n" +
                "            }\n" +
                "        ],\n" +
                "        \"jp.biz.trip.fat4.tripqate.com\": [\n" +
                "            {\n" +
                "                \"language\": \"en-US\",\n" +
                "                \"sharkKey\": \"key.corp.public.ibc.layout.us\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"language\": \"ja-JP\",\n" +
                "                \"sharkKey\": \"key.corp.public.ibc.layout.jp\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"language\": \"zh-HK\",\n" +
                "                \"sharkKey\": \"key.corp.public.ibc.layout.hk\"\n" +
                "            }\n" +
                "        ],\n" +
                "        \"jp.biz.trip.fat11.tripqate.com\": [\n" +
                "            {\n" +
                "                \"language\": \"en-US\",\n" +
                "                \"sharkKey\": \"key.corp.public.ibc.layout.us\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"language\": \"ja-JP\",\n" +
                "                \"sharkKey\": \"key.corp.public.ibc.layout.jp\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"language\": \"zh-HK\",\n" +
                "                \"sharkKey\": \"key.corp.public.ibc.layout.hk\"\n" +
                "            }\n" +
                "        ],\n" +
                "        \"hk.biz.trip.fat4.tripqate.com\": [\n" +
                "            {\n" +
                "                \"language\": \"en-US\",\n" +
                "                \"sharkKey\": \"key.corp.public.ibc.layout.us\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"language\": \"ja-JP\",\n" +
                "                \"sharkKey\": \"key.corp.public.ibc.layout.jp\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"language\": \"zh-HK\",\n" +
                "                \"sharkKey\": \"key.corp.public.ibc.layout.hk\"\n" +
                "            }\n" +
                "        ],\n" +
                "        \"hk.biz.trip.fat11.tripqate.com\": [\n" +
                "            {\n" +
                "                \"language\": \"en-US\",\n" +
                "                \"sharkKey\": \"key.corp.public.ibc.layout.us\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"language\": \"ja-JP\",\n" +
                "                \"sharkKey\": \"key.corp.public.ibc.layout.jp\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"language\": \"zh-HK\",\n" +
                "                \"sharkKey\": \"key.corp.public.ibc.layout.hk\"\n" +
                "            }\n" +
                "        ],\n" +
                "        \"sg.biz.trip.fat4.tripqate.com\": [\n" +
                "            {\n" +
                "                \"language\": \"en-US\",\n" +
                "                \"sharkKey\": \"key.corp.public.ibc.layout.us\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"language\": \"ja-JP\",\n" +
                "                \"sharkKey\": \"key.corp.public.ibc.layout.jp\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"language\": \"zh-HK\",\n" +
                "                \"sharkKey\": \"key.corp.public.ibc.layout.hk\"\n" +
                "            }\n" +
                "        ],\n" +
                "        \"sg.biz.trip.fat11.tripqate.com\": [\n" +
                "            {\n" +
                "                \"language\": \"en-US\",\n" +
                "                \"sharkKey\": \"key.corp.public.ibc.layout.us\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"language\": \"ja-JP\",\n" +
                "                \"sharkKey\": \"key.corp.public.ibc.layout.jp\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"language\": \"zh-HK\",\n" +
                "                \"sharkKey\": \"key.corp.public.ibc.layout.hk\"\n" +
                "            }\n" +
                "        ],\n" +
                "        \"tokyo-master.fat4.tripqate.com\": [\n" +
                "            {\n" +
                "                \"language\": \"ja-JP\",\n" +
                "                \"sharkKey\": \"key.corp.public.ibc.layout.jp\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"language\": \"en-US\",\n" +
                "                \"sharkKey\": \"key.corp.public.ibc.layout.us\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"language\": \"zh-HK\",\n" +
                "                \"sharkKey\": \"key.corp.public.ibc.layout.hk\"\n" +
                "            }\n" +
                "        ],\n" +
                "        \"tokyo-master.fat11.tripqate.com\": [\n" +
                "            {\n" +
                "                \"language\": \"ja-JP\",\n" +
                "                \"sharkKey\": \"key.corp.public.ibc.layout.jp\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"language\": \"en-US\",\n" +
                "                \"sharkKey\": \"key.corp.public.ibc.layout.us\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"language\": \"zh-HK\",\n" +
                "                \"sharkKey\": \"key.corp.public.ibc.layout.hk\"\n" +
                "            }\n" +
                "        ],\n" +
                "        \"tokyo-master.fat24.tripqate.com\": [\n" +
                "            {\n" +
                "                \"language\": \"ja-JP\",\n" +
                "                \"sharkKey\": \"key.corp.public.ibc.layout.jp\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"language\": \"en-US\",\n" +
                "                \"sharkKey\": \"key.corp.public.ibc.layout.us\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"language\": \"zh-HK\",\n" +
                "                \"sharkKey\": \"key.corp.public.ibc.layout.hk\"\n" +
                "            }\n" +
                "        ],\n" +
                "        \"vn.biz.trip.fat11.tripqate.com\": [\n" +
                "            {\n" +
                "                \"language\": \"en-US\",\n" +
                "                \"sharkKey\": \"key.corp.public.ibc.layout.us\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"language\": \"ja-JP\",\n" +
                "                \"sharkKey\": \"key.corp.public.ibc.layout.jp\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"language\": \"zh-HK\",\n" +
                "                \"sharkKey\": \"key.corp.public.ibc.layout.hk\"\n" +
                "            }\n" +
                "        ],\n" +
                "        \"vn.biz.trip.fat4.tripqate.com\": [\n" +
                "            {\n" +
                "                \"language\": \"en-US\",\n" +
                "                \"sharkKey\": \"key.corp.public.ibc.layout.us\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"language\": \"ja-JP\",\n" +
                "                \"sharkKey\": \"key.corp.public.ibc.layout.jp\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"language\": \"zh-HK\",\n" +
                "                \"sharkKey\": \"key.corp.public.ibc.layout.hk\"\n" +
                "            }\n" +
                "        ],\n" +
                "        \"vn.biz.trip.fat24.tripqate.com\": [\n" +
                "            {\n" +
                "                \"language\": \"en-US\",\n" +
                "                \"sharkKey\": \"key.corp.public.ibc.layout.us\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"language\": \"ja-JP\",\n" +
                "                \"sharkKey\": \"key.corp.public.ibc.layout.jp\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"language\": \"zh-HK\",\n" +
                "                \"sharkKey\": \"key.corp.public.ibc.layout.hk\"\n" +
                "            }\n" +
                "        ],\n" +
                "        \"totalLangUageByHost\": [\n" +
                "            {\n" +
                "                \"language\": \"en-US\",\n" +
                "                \"sharkKey\": \"key.corp.public.ibc.layout.us\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"language\": \"ja-JP\",\n" +
                "                \"sharkKey\": \"key.corp.public.ibc.layout.jp\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"language\": \"zh-HK\",\n" +
                "                \"sharkKey\": \"key.corp.public.ibc.layout.hk\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"language\": \"zh-CN\",\n" +
                "                \"sharkKey\": \"key.corp.public.ibc.layout.hk\"\n" +
                "            }\n" +
                "        ]\n" +
                "    },\n" +
                "    \"posConfigMap\": {\n" +
                "        \"ja-JP\": [\n" +
                "            {\n" +
                "                \"language\": \"en-US\",\n" +
                "                \"sharkKey\": \"key.corp.public.ibc.layout.us\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"language\": \"ja-JP\",\n" +
                "                \"sharkKey\": \"key.corp.public.ibc.layout.jp\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"language\": \"zh-HK\",\n" +
                "                \"sharkKey\": \"key.corp.public.ibc.layout.hk\"\n" +
                "            }\n" +
                "        ],\n" +
                "        \"zh-HK\": [\n" +
                "            {\n" +
                "                \"language\": \"en-US\",\n" +
                "                \"sharkKey\": \"key.corp.public.ibc.layout.us\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"language\": \"ja-JP\",\n" +
                "                \"sharkKey\": \"key.corp.public.ibc.layout.jp\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"language\": \"zh-HK\",\n" +
                "                \"sharkKey\": \"key.corp.public.ibc.layout.hk\"\n" +
                "            }\n" +
                "        ],\n" +
                "        \"en-US\": [\n" +
                "            {\n" +
                "                \"language\": \"en-US\",\n" +
                "                \"sharkKey\": \"key.corp.public.ibc.layout.us\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"language\": \"ja-JP\",\n" +
                "                \"sharkKey\": \"key.corp.public.ibc.layout.jp\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"language\": \"zh-HK\",\n" +
                "                \"sharkKey\": \"key.corp.public.ibc.layout.hk\"\n" +
                "            }\n" +
                "        ],\n" +
                "        \"zh-CN\": [\n" +
                "            {\n" +
                "                \"language\": \"en-US\",\n" +
                "                \"sharkKey\": \"key.corp.public.ibc.layout.us\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"language\": \"ja-JP\",\n" +
                "                \"sharkKey\": \"key.corp.public.ibc.layout.jp\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"language\": \"zh-HK\",\n" +
                "                \"sharkKey\": \"key.corp.public.ibc.layout.hk\"\n" +
                "            }\n" +
                "        ],\n" +
                "        \"en-SG\": [\n" +
                "            {\n" +
                "                \"language\": \"en-US\",\n" +
                "                \"sharkKey\": \"key.corp.public.ibc.layout.us\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"language\": \"ja-JP\",\n" +
                "                \"sharkKey\": \"key.corp.public.ibc.layout.jp\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"language\": \"zh-HK\",\n" +
                "                \"sharkKey\": \"key.corp.public.ibc.layout.hk\"\n" +
                "            }\n" +
                "        ],\n" +
                "        \"en-XX\": [\n" +
                "            {\n" +
                "                \"language\": \"en-US\",\n" +
                "                \"sharkKey\": \"key.corp.public.ibc.layout.us\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"language\": \"ja-JP\",\n" +
                "                \"sharkKey\": \"key.corp.public.ibc.layout.jp\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"language\": \"zh-HK\",\n" +
                "                \"sharkKey\": \"key.corp.public.ibc.layout.hk\"\n" +
                "            }\n" +
                "        ],\n" +
                "        \"vi-VN\": [\n" +
                "            {\n" +
                "                \"language\": \"en-US\",\n" +
                "                \"sharkKey\": \"key.corp.public.ibc.layout.us\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"language\": \"ja-JP\",\n" +
                "                \"sharkKey\": \"key.corp.public.ibc.layout.jp\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"language\": \"zh-HK\",\n" +
                "                \"sharkKey\": \"key.corp.public.ibc.layout.hk\"\n" +
                "            }\n" +
                "        ]\n" +
                "    },\n" +
                "    \"posConfigMapForApp\": {\n" +
                "        \"default\": [\n" +
                "            {\n" +
                "                \"language\": \"en-US\",\n" +
                "                \"sharkKey\": \"key.corp.public.ibc.layout.us\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"language\": \"ja-JP\",\n" +
                "                \"sharkKey\": \"key.corp.public.ibc.layout.jp\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"language\": \"zh-HK\",\n" +
                "                \"sharkKey\": \"key.corp.public.ibc.layout.hk\"\n" +
                "            }\n" +
                "        ]\n" +
                "    },\n" +
                "    \"brandConfigMap\": {\n" +
                "        \"C3\": [\n" +
                "            {\n" +
                "                \"language\": \"en-US\",\n" +
                "                \"sharkKey\": \"key.corp.public.ibc.layout.us\"\n" +
                "            }\n" +
                "        ]\n" +
                "    }\n" +
                "}", LanguageConfig.class)
        mapperOfLanguageList.languageConfig = languageConfig1


        when:

        def result = mapperOfLanguageList.convert(Tuple5.of(new TemplateSoaRequestType(header: new TemplateHeader(corpId: "1")), new GetCorpInfoResponseType(pos: 'ja-JP'), new GeneralSearchAccountInfoResponseType(), null, new GetCorpUserInfoResponseType()))
        then:
        3 == result.size()

    }

    @Unroll
    def legalDocumentQueryTest1() {
        given:
        1 == 1
        when:
        def result = mapperOfLanguageList.convert(Tuple5.of(new TemplateSoaRequestType(header: new TemplateHeader(corpId: "1"), gatewayHost: "111"), new GetCorpInfoResponseType(), new GeneralSearchAccountInfoResponseType(), "1", new GetCorpUserInfoResponseType()))
        then:
        result == null
    }

    @Unroll
    def "test isCustomScene method with scene: #scene, expected: #expected"() {
        when:
        def result = mapperOfLanguageList.isCustomScene(scene)

        then:
        result == expected

        where:
        scene           | expected
        null            | false
        ""              | false
        "   "           | false
        "BOOKING_USER"  | false
        "booking_user"  | false
        "Booking_User"  | false
        "TRIP_BIZ_ALL"  | true
        "TM_ALL"        | true
        "BST_ALL"       | true
        "CUSTOM_SCENE"  | true
        "any_other"     | true
    }

    @Unroll
    def "test buildPreferredLanguage method with scene: #scene, userResponse: #userResponseDesc, expected: #expected"() {
        when:
        def result = mapperOfLanguageList.buildPreferredLanguage(userResponse, scene)

        then:
        result == expected

        where:
        scene           | userResponse                                                    | userResponseDesc           | expected
        null            | null                                                           | "null"                     | null
        ""              | null                                                           | "null"                     | null
        "   "           | null                                                           | "null"                     | null
        "BOOKING_USER"  | null                                                           | "null"                     | null
        "BOOKING_USER"  | new GetCorpUserInfoResponseType()                             | "empty response"           | null
        "BOOKING_USER"  | new GetCorpUserInfoResponseType(preferredLanguage: "en-US")   | "with preferred language"  | "en-US"
        "BOOKING_USER"  | new GetCorpUserInfoResponseType(preferredLanguage: "zh-CN")   | "with zh-CN language"      | "zh-CN"
        "booking_user"  | new GetCorpUserInfoResponseType(preferredLanguage: "ja-JP")   | "lowercase scene"          | "ja-JP"
        "Booking_User"  | new GetCorpUserInfoResponseType(preferredLanguage: "fr-FR")   | "mixed case scene"         | "fr-FR"
        "TRIP_BIZ_ALL"  | new GetCorpUserInfoResponseType(preferredLanguage: "en-US")   | "non-booking scene"        | null
        "TM_ALL"        | new GetCorpUserInfoResponseType(preferredLanguage: "zh-CN")   | "another non-booking"      | null
        "CUSTOM_SCENE"  | new GetCorpUserInfoResponseType(preferredLanguage: "ja-JP")   | "custom scene"             | null
    }
}